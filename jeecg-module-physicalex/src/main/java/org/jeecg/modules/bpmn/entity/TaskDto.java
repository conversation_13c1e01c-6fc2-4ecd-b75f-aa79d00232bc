package org.jeecg.modules.bpmn.entity;

import lombok.Data;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.task.Task;

import java.util.Date;
import java.util.Map;

@Data
public class TaskDto {
    private String id;
    private String name;
    private String assignee;
    private String formKey;
    private String processInstanceId;
    private String processDefinitionId;
    private String description;
    private String businessKey;
    private int priority;
    private Date dueDate;
    private Date createTime;
    private String owner;
    private String delegationState;
    private Map<String, Object> variables;

}
