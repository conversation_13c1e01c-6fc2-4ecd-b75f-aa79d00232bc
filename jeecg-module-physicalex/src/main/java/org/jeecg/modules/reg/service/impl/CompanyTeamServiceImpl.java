package org.jeecg.modules.reg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.utils.SequenceGenerator;
import org.jeecg.modules.reg.entity.CompanyTeam;
import org.jeecg.modules.reg.entity.CompanyTeamItemGroup;
import org.jeecg.modules.reg.mapper.CompanyTeamMapper;
import org.jeecg.modules.reg.service.ICompanyTeamItemGroupService;
import org.jeecg.modules.reg.service.ICompanyTeamService;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 单位分组
 * @Author: jeecg-boot
 * @Date:   2024-02-19
 * @Version: V1.0
 */
@Service
public class CompanyTeamServiceImpl extends ServiceImpl<CompanyTeamMapper, CompanyTeam> implements ICompanyTeamService {
	
	@Autowired
	private CompanyTeamMapper companyTeamMapper;
	@Autowired
	private JdbcTemplate jdbcTemplate;
	@Autowired
	private SequenceGenerator sequenceGenerator;
	@Autowired
	private ICompanyTeamItemGroupService companyTeamItemGroupService;
	
	@Override
	public List<CompanyTeam> selectByMainId(String mainId) {
		return companyTeamMapper.selectByMainId(mainId);
	}

	@Override
	public void deleteBatchItemGroup(String companyRegId, String teamId, List<String> itemgroupIds) {
		jdbcTemplate.batchUpdate("delete from company_team_item_group where company_reg_id = ? and team_id = ? and item_group_id = ?", itemgroupIds, itemgroupIds.size(), (ps, argument) -> {
			ps.setString(1, companyRegId);
			ps.setString(2, teamId);
			ps.setString(3, argument);
		});
	}

	@Override
	public void fillTeamNum(CompanyTeam companyTeam) {
		if(companyTeam == null){
			return;
		}
		if(StringUtils.isEmpty(companyTeam.getTeamNum())){
			companyTeam.setTeamNum(sequenceGenerator.getFormatedSerialNumBaseToday("team_num"));
		}
	}

	@Override
	public void copyCompanyTeam(String teamId) {
		CompanyTeam companyTeam = getById(teamId);
		companyTeam.setId(null);
		companyTeam.setName(companyTeam.getName()+"-副本");
		companyTeam.setTeamNum(null);
		fillTeamNum(companyTeam);
		save(companyTeam);
		//查询项目
		List<CompanyTeamItemGroup> list = companyTeamItemGroupService.list(new LambdaQueryWrapper<CompanyTeamItemGroup>().eq(CompanyTeamItemGroup::getTeamId, teamId));
		if (CollectionUtils.isNotEmpty(list)) {
			list.forEach(companyTeamItemGroup -> {
				companyTeamItemGroup.setId(null);
				companyTeamItemGroup.setTeamId(companyTeam.getId());

			});
			companyTeamItemGroupService.saveBatch(list);
		}


	}
}
