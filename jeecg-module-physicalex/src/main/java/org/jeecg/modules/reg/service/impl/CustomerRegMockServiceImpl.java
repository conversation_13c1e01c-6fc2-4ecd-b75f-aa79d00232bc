package org.jeecg.modules.reg.service.impl;

import org.jeecg.common.aspect.annotation.AutoDict;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.reg.service.ICustomerRegMockService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.station.service.ICustomerRegItemResultService;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.service.ICustomerRegSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CustomerRegMockServiceImpl implements ICustomerRegMockService {
    @Autowired
    private ICustomerRegService regService;
    @Autowired
    private ICustomerRegItemGroupService regItemGroupService;
    @Autowired
    private ICustomerRegItemResultService regItemResultService;
    @Autowired
    private ICustomerRegSummaryService regSummaryService;


    @Override
    public void generateData(String regId) {
        if (regId == null) {
            return;
        }
        //获取指定id的客户登记信息
        CustomerReg reg = regService.getById(regId);
        //获取指定id的客户登记项目组信息
        List<CustomerRegItemGroup> regItemGroups = regItemGroupService.listByReg(regId, null);
        //获取指定id的客户登记项目结果信息
        List<CustomerRegItemResult> regItemResults = regItemResultService.listByRegId(regId);
        //获取指定id的客户登记汇总信息
        CustomerRegSummary summary = regSummaryService.getByRegId(regId);

        //模拟生成数据,1000*365*20
        if(reg == null){
            return;
        }
        System.out.println("模拟生成20年的数据");
        for (int i = 0; i < 1000 * 365 * 20; i++) {
            reg.setId(null);
            regService.save(reg);
            for (CustomerRegItemGroup group : regItemGroups) {
                group.setId(null);
                group.setCustomerRegId(reg.getId());
                regItemGroupService.save(group);
            }
            for (CustomerRegItemResult result : regItemResults) {
                result.setId(null);
                result.setCustomerRegId(reg.getId());
                regItemResultService.save(result);
            }

            summary.setId(null);
            summary.setCustomerRegId(reg.getId());
            regSummaryService.save(summary);
        }


    }
}
