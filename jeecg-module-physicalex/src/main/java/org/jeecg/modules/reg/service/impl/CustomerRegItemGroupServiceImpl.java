package org.jeecg.modules.reg.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mzlion.easyokhttp.HttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.ExApiConstants;
import org.jeecg.modules.basicinfo.entity.ItemGroupRelation;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.datamanage.entity.RegGroupChangeRecord;
import org.jeecg.modules.datamanage.mapper.RegGroupChangeRecordMapper;
import org.jeecg.modules.reg.bo.DepartAndGroupBean;
import org.jeecg.modules.reg.bo.DepartGroupTree;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.interfacecheck.entity.InterfaceResult;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: customer_reg_item_group
 * @Author: jeecg-boot
 * @Date: 2024-04-03
 * @Version: V1.0
 */
@Slf4j
@Service
public class CustomerRegItemGroupServiceImpl extends ServiceImpl<CustomerRegItemGroupMapper, CustomerRegItemGroup> implements ICustomerRegItemGroupService {
    @Autowired
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Autowired
    private SysDepartMapper sysDepartMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private RegGroupChangeRecordMapper regGroupChangeRecordMapper;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private ISysSettingService sysSettingService;

    @Override
    public JSONObject statReg() {

        //统计今日体检登记人数
        Integer todayRegCount = jdbcTemplate.queryForObject("SELECT COUNT(1) FROM customer_reg WHERE TO_DAYS(reg_time) = TO_DAYS(NOW())", Integer.class);
        //统计体检登记人数
        //Integer totalRegCount = jdbcTemplate.queryForObject("SELECT COUNT(1) FROM customer_reg", Integer.class);

        //统计今日体检费用
        Double todayRegPrice = jdbcTemplate.queryForObject("SELECT SUM(g.price_after_dis) FROM customer_reg_item_group g join customer_reg c on g.customer_reg_id=c.id WHERE TO_DAYS(c.reg_time) = TO_DAYS(NOW()) and add_minus_flag!=-1 and ((payer_type='个人支付' and payment_state='已支付' ) or payer_type='单位支付')", Double.class);

        //统计危急值人数
        Double totalCriticalCount = jdbcTemplate.queryForObject("select count( distinct customer_reg_id)  from customer_reg_critical_item where TO_DAYS(NOW())-TO_DAYS(create_time)=0", Double.class);

        //统计报告超时人数
        Double totalReportExpiredCount = jdbcTemplate.queryForObject("SELECT COUNT(1) FROM customer_reg_item_group WHERE check_status='已出报告' and add_minus_flag!=-1 and TO_DAYS(NOW())-TO_DAYS(report_time)>2", Double.class);

        JSONObject stat = new JSONObject();
        stat.put("todayRegCount", todayRegCount);
        stat.put("todayRegPrice", todayRegPrice);
        stat.put("totalCriticalCount", totalCriticalCount);
        stat.put("totalReportExpiredCount", totalReportExpiredCount);
        return stat;
    }

    @Override
    public List<CustomerRegItemGroup> listByRegAndDepart(String customerRegId, List<String> departmentIds) {
        return customerRegItemGroupMapper.listByRegAndDepart(customerRegId, departmentIds);
    }

    @Override
    public List<CustomerRegItemGroup> listByReg(String customerRegId, String payStatus) {
        return customerRegItemGroupMapper.listByReg(customerRegId, payStatus);
    }

    @Override
    public List<CustomerRegItemGroup> listWithItemGroupByReg(String customerRegId, List<String> departmentIds, Boolean containFeeOnly) {
        return customerRegItemGroupMapper.listWithItemGroupByReg(customerRegId, departmentIds, containFeeOnly);
    }

    @Override
    public List<CustomerRegItemGroup> listUnpaidByReg(String customerRegId) {
        LambdaQueryWrapper<CustomerRegItemGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(CustomerRegItemGroup::getBillId);
        queryWrapper.eq(CustomerRegItemGroup::getCustomerRegId, customerRegId);
        queryWrapper.ne(CustomerRegItemGroup::getAddMinusFlag, -1);
        queryWrapper.orderByDesc(CustomerRegItemGroup::getCreateTime);
        return list(queryWrapper);
    }


    @Override
    @Cacheable(value = "customerRegGroupConslusion", key = "#id", unless = "#result == null")
    public String getCheckConclusion(String id) {
        return customerRegItemGroupMapper.getCheckConclusion(id);
    }

    @Override
    @Cacheable(value = "customerRegGroup", key = "#regId + '_' + #groupId", unless = "#result == null")
    public CustomerRegItemGroup getByRegAndGroup(String regId, String groupId) {
        return customerRegItemGroupMapper.getByRegAndGroup(regId, groupId);
    }

    @Override
    public List<DepartAndGroupBean> list4RetrieveGuideSheet(String regId) {

        List<CustomerRegItemGroup> groupList = listWithItemGroupByReg(regId, null, false);
        //按照科室分组
        List<DepartAndGroupBean> departAndGroupBeans = new java.util.ArrayList<>(groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getDepartmentId)).entrySet().stream().map(entry -> {
            DepartAndGroupBean departAndGroupBean = new DepartAndGroupBean();
            SysDepart depart = sysDepartMapper.selectById(entry.getKey());
            departAndGroupBean.setDepart(depart);
            departAndGroupBean.setGroupList(entry.getValue());
            return departAndGroupBean;
        }).toList());

        //按照科室的guideSort排序
        //departAndGroupBeans.sort(Comparator.comparingInt(o -> o.getDepart().getGuideSort()));

        departAndGroupBeans.sort(Comparator.comparingInt((DepartAndGroupBean o) -> {
            SysDepart depart = o.getDepart();
            return (depart != null && depart.getGuideSort() != null) ? depart.getGuideSort() : Integer.MAX_VALUE;
        }));
        return departAndGroupBeans;
    }

    @Override
    public List<DepartGroupTree> listDepartGroupTree(String regId) {
        List<DepartAndGroupBean> departAndGroupBeans = list4RetrieveGuideSheet(regId);

        return departAndGroupBeans.stream().filter(departAndGroupBean -> departAndGroupBean.getDepart() != null).map(departAndGroupBean -> {
            DepartGroupTree departGroupTree = new DepartGroupTree();
            departGroupTree.setKey(departAndGroupBean.getDepart().getId());
            departGroupTree.setName(departAndGroupBean.getDepart().getDepartName());
            departGroupTree.setId(departAndGroupBean.getDepart().getId());

            //统计groupList内的checkStatus，格式为：状态名：数量
            String checkStatus = departAndGroupBean.getGroupList().stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getCheckStatus, Collectors.counting())).entrySet().stream().map(entry -> entry.getKey() + ":" + entry.getValue()).collect(Collectors.joining(","));
            departGroupTree.setCheckStatus(checkStatus);
            departGroupTree.setType("DEPARTMENT");

            List<DepartGroupTree> children = departAndGroupBean.getGroupList().stream().map(group -> {
                DepartGroupTree child = new DepartGroupTree();
                child.setKey(group.getItemGroupId());
                child.setName(group.getItemGroupName());
                child.setId(group.getItemGroupId());
                child.setCheckStatus(group.getCheckStatus());
                child.setAbandonStatus(String.valueOf(group.getAbandonFlag()));
                child.setItemGroup(group.getItemGroup());
                child.setType("GROUP");
                child.setAbnormalFlag(group.getAbnormalFlag());
                return child;
            }).collect(Collectors.toList());

            departGroupTree.setChildren(children);
            return departGroupTree;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateApplyPrintTimes(List<String> groupIds) {
        if (groupIds == null || groupIds.isEmpty()) {
            return;
        }
        String placeholders = String.join(",", Collections.nCopies(groupIds.size(), "?"));
        String sql = "UPDATE customer_reg_item_group SET apply_print_times = apply_print_times + 1 WHERE id IN (" + placeholders + ")";
        Object[] params = groupIds.toArray(new Object[0]);
        jdbcTemplate.update(sql, params);
    }

    @Override
    public InterfaceResult getInterfaceResultByRegGroupId(String regGroupId) {
        return customerRegItemGroupMapper.getInterfaceResultByRegGroupId(regGroupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(CustomerRegItemGroup customerRegItemGroup) {
        CustomerRegItemGroup oldGroup = customerRegItemGroupMapper.selectById(customerRegItemGroup.getId());
        CustomerReg customerReg = customerRegMapper.selectById(oldGroup.getCustomerRegId());
        LambdaUpdateWrapper<CustomerRegItemGroup> updateWrapper = new LambdaUpdateWrapper<CustomerRegItemGroup>()
                .set(CustomerRegItemGroup::getCheckStatus, customerRegItemGroup.getCheckStatus())
                .set(CustomerRegItemGroup::getInterfaceSyncStatus, customerRegItemGroup.getInterfaceSyncStatus())
                .set(CustomerRegItemGroup::getPicSyncStatus, customerRegItemGroup.getPicSyncStatus())
                .set(CustomerRegItemGroup::getPayStatus, customerRegItemGroup.getPayStatus())
                .eq(CustomerRegItemGroup::getId, oldGroup.getId());
        boolean update = update(updateWrapper);
        if (update){
            RegGroupChangeRecord groupChangeRecord = new RegGroupChangeRecord();
            groupChangeRecord.setCustomerName(customerReg.getName());
            groupChangeRecord.setExamNo(oldGroup.getExamNo());
            groupChangeRecord.setRegGroupId(oldGroup.getId());
            groupChangeRecord.setRegGroupName(oldGroup.getItemGroupName());
            groupChangeRecord.setOldCheckStatus(oldGroup.getCheckStatus());
            groupChangeRecord.setOldInferfaceStatus(oldGroup.getInterfaceSyncStatus());
            groupChangeRecord.setNewCheckStatus(customerRegItemGroup.getCheckStatus());
            groupChangeRecord.setNewInterfaceStatus(customerRegItemGroup.getInterfaceSyncStatus());
            groupChangeRecord.setReason(customerRegItemGroup.getChangeReason());
            groupChangeRecord.setChangePic(customerRegItemGroup.getChangePic());
            groupChangeRecord.setCreateTime(new Date());
            groupChangeRecord.setCheckPartCode(oldGroup.getCheckPartCode());
            groupChangeRecord.setCheckPartName(oldGroup.getCheckPartName());
            regGroupChangeRecordMapper.insert(groupChangeRecord);
        }
    }


    @Override
    public void sendItemGroup2Interface(List<CustomerRegItemGroup> groups) throws Exception{
        String sendItemGroupFlag = sysSettingService.getValueByCode("send_item_group_flag");
        if (!StringUtils.equals(sendItemGroupFlag, "1")) {
            return ;
        }
        if (CollectionUtils.isEmpty(groups)) {
            return ;
        }
        String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
        if (StringUtils.isBlank(hipInterfaceUrl)) {
            throw new Exception("HIP接口地址未配置");
        }
        hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");

        String finalUrl = hipInterfaceUrl + ExApiConstants.SEND_ITEM_GROUP_PATH;
        log.info("调用" + finalUrl + " 数据：" + JSON.toJSONString(groups));
        String resultStr = HttpClient.textBody(finalUrl).header("Content-Type", "application/json").json(JSON.toJSONString(groups)).execute().asString();

        log.info("调用" + finalUrl + " 返回：" + resultStr);
        JSONObject resultJson = JSON.parseObject(resultStr);
        if (resultJson == null) {
            throw new Exception("HIP接口调用异常");
        }

        if (resultJson.getInteger("code") != 0) {
            throw new Exception("HIP接口调用异常，详情：" + resultJson.getString("msg"));
        }

    }

    @Override
    public Boolean checkIsSummary(String customerRegId) {
        try {
            String sql = "SELECT 1 " +
                            "FROM customer_reg_item_group g " +
                            "WHERE g.customer_reg_id = ? " +
                            "  AND g.pay_status != '退款成功' " +
                            "  AND g.add_minus_flag != -1 " +
                            "  AND g.abandon_flag = 0 " +
                            "AND EXISTS ( " +
                            "    SELECT 1 " +
                            "    FROM item_group ig " +
                            "    WHERE ig.id = g.item_group_id " +
                            "    AND ig.summary_flag = 1 " +
                            ") " +
                            "LIMIT 1";
            List<String> result = jdbcTemplate.queryForList(sql,String.class,customerRegId);
            return CollectionUtils.isNotEmpty(result);
        } catch (DataAccessException e) {
            return false;
        }
    }


    @Override
    public List<ItemGroupRelation> getRelationGroupByRegIdAndGroupId(String customerRegId, String groupId) {
        List<ItemGroupRelation> relationGroup =customerRegItemGroupMapper.getRelationGroupByRegIdAndGroupId(customerRegId,groupId);
        return relationGroup;
    }
}
