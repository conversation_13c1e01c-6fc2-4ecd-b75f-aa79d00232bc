package org.jeecg.modules.psy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.psy.entity.PsyCard;
import org.jeecg.modules.psy.entity.PsyCardSetting;
import org.jeecg.modules.psy.mapper.PsyCardMapper;
import org.jeecg.modules.psy.mapper.PsyCardSettingMapper;
import org.jeecg.modules.psy.service.IPsyCardService;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.mapper.SysTenantMapper;
import org.jeecg.modules.system.mapper.SysUserMapper;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.ModelAndView;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 心理测评卡
 * @Author: jeecg-boot
 * @Date: 2023-12-21
 * @Version: V1.0
 */
@Service
public class PsyCardServiceImpl extends ServiceImpl<PsyCardMapper, PsyCard> implements IPsyCardService {

    @Autowired
    private PsyCardMapper psyCardMapper;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private PsyCardSettingMapper psyCardSettingMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private CardGenLockService cardGenLockService;
    @Autowired
    private SysTenantMapper sysTenantMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public PsyCard getCardByAccount(String account) throws Exception {

        QueryWrapper<PsyCard> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account", account);
        queryWrapper.last("limit 1");
        PsyCard psyCard = psyCardMapper.selectOne(queryWrapper);
        if (psyCard == null) {
            throw new Exception("卡号不存在！");
        }
        return psyCard;
    }

    @Override
    public List<PsyCard> batchAddCard(Integer count, String tenantId) throws Exception {

        PsyCardSetting psyCardSetting = psyCardSettingMapper.getCardSettingByTenantId(tenantId);
        if (psyCardSetting == null) {
            throw new Exception("没有默认的卡片设置！");
        }

        if (cardGenLockService.tryLock()) {

            Integer cachedCardNo = (Integer) redisUtil.get("cardNo:" + tenantId);
            if (cachedCardNo == null) {
                cachedCardNo = jdbcTemplate.queryForObject("select max(account_no) from card where tenant_id = ?", Integer.class, tenantId);
                if (cachedCardNo == null) {
                    cachedCardNo = 0;
                }
                redisUtil.set("cardNo:" + tenantId, cachedCardNo);
            }

            String acountPrefix = psyCardSetting.getCardPrefix();
            List<PsyCard> psyCards = new ArrayList<>();
            List<SysUser> sysUsers = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                PsyCard psyCard = new PsyCard();
                psyCard.setTenantId(tenantId);
                psyCard.setCardSettingId(psyCardSetting.getId());
                psyCard.setCardNo(cachedCardNo + i + 1);
                //拼接账号，默认6位，如果超过6位递增
                String account = null;
                if (psyCard.getCardNo() > 999999) {
                    account = acountPrefix + psyCard.getCardNo();
                } else {
                    account = acountPrefix + String.format("%06d", psyCard.getCardNo());
                }
                psyCard.setAccount(account);
                psyCard.setPwd(generatePwd());
                psyCard.setStatus("待制卡");
                psyCard.setDurationMonths(psyCardSetting.getDurationMonths());
                psyCards.add(psyCard);

                SysUser user = new SysUser();
                user.setCreateTime(new Date());//设置创建时间
                String salt = oConvertUtils.randomGen(8);
                user.setSalt(salt);
                String passwordEncode = PasswordUtil.encrypt(user.getUsername(), user.getPassword(), salt);
                user.setPassword(passwordEncode);
                user.setStatus(1);
                user.setDelFlag(CommonConstant.DEL_FLAG_0);
                user.setOrgCode(null);
            }
            saveBatch(psyCards);

            redisUtil.incr("cardNo:" + tenantId, count);
            cardGenLockService.unlock();
            return psyCards;
        } else {
            throw new Exception("请5分钟后重试！");
        }
    }

    @Override
    public PsyCard generateAddCard(Integer count) throws Exception {

        PsyCardSetting psyCardSetting = psyCardSettingMapper.getCardSettingByTenantId(null);
        if (psyCardSetting == null) {
            throw new Exception("没有默认的卡片设置！");
        }
        if (cardGenLockService.tryLock()) {

            Integer cachedCardNo = (Integer) redisUtil.get("psyCardNo");
            if (cachedCardNo == null) {
                cachedCardNo = jdbcTemplate.queryForObject("select max(card_no) from card", Integer.class);
                if (cachedCardNo == null) {
                    cachedCardNo = 0;
                }
                redisUtil.set("psyCardNo", cachedCardNo);
            }

            String acountPrefix = psyCardSetting.getCardPrefix();
//            List<PsyCard> psyCards = new ArrayList<>();
//            List<SysUser> sysUsers = new ArrayList<>();
                PsyCard psyCard = new PsyCard();
                psyCard.setCardSettingId(psyCardSetting.getId());
                psyCard.setCardNo(cachedCardNo + 1);
                //拼接账号，默认6位，如果超过6位递增
                String account = null;
                if (psyCard.getCardNo() > 999999) {
                    account = acountPrefix + psyCard.getCardNo();
                } else {
                    account = acountPrefix + String.format("%06d", psyCard.getCardNo());
                }
                psyCard.setAccount(account);
                psyCard.setPwd(generatePwd());
                psyCard.setStatus("待激活");
                psyCard.setDelFlag("0");
                psyCard.setDurationMonths(psyCardSetting.getDurationMonths());
//                psyCards.add(psyCard);

         /*       SysUser user = new SysUser();
                user.setCreateTime(new Date());//设置创建时间
                String salt = oConvertUtils.randomGen(8);
                user.setSalt(salt);
                String passwordEncode = PasswordUtil.encrypt(user.getUsername(), user.getPassword(), salt);
                user.setPassword(passwordEncode);
                user.setStatus(1);
                user.setDelFlag(CommonConstant.DEL_FLAG_0);
                user.setOrgCode(null);*/
//            saveBatch(psyCards);
            save(psyCard);

            redisUtil.incr("psyCardNo", count);
            cardGenLockService.unlock();
            return psyCard;
        } else {
            throw new Exception("请5分钟后重试！");
        }
    }

    @Override
    public ModelAndView export(List<PsyCard> psyCardList) {
        //AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "心理测评卡");
        mv.addObject(NormalExcelConstants.CLASS, PsyCard.class);
        //update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        ExportParams exportParams = new ExportParams("心理测评卡", "导出人:" + loginUser.getRealname(), "心理测评卡");
        //update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.DATA_LIST, psyCardList);
        return mv;
    }

    @Override
    public void active(PsyCard psyCard) {
        psyCard.setStatus("已激活");
        psyCard.setActiveTime(new Date());
        psyCardMapper.updateById(psyCard);
    }


//    public String generatePwd() {
//        SecureRandom secureRandom = new SecureRandom();
//        char[] chars = "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray();
//        char[] password = new char[6];
//        for (int i = 0; i < 6; i++) {
//            password[i] = chars[secureRandom.nextInt(chars.length)];
//        }
//        return new String(password);
//    }
    public String generatePwd() {
        SecureRandom secureRandom = new SecureRandom();
        return String.format("%06d", secureRandom.nextInt(1000000));
    }
}
