server:
  port: 7001
  
spring:
  application:
    name: jeecg-system
  cloud:
    nacos:
      config:
        server-addr: @config.server-addr@
        group: @config.group@
        namespace: @config.namespace@
        username: @config.username@
        password: @config.password@
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        group: @config.group@
        namespace: @config.namespace@
        username: @config.username@
        password: @config.password@
  config:
    import:
      - optional:nacos:jeecg.yaml
      - optional:nacos:<EMAIL>@.yaml